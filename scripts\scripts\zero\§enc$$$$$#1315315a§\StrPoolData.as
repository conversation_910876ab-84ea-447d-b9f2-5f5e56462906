package zero.§enc$$$$$#1315315a§
{
   import flash.utils.ByteArray;
   
   [Embed(source="/_assets/4_zero.enc$$$$$#1315315a.StrPoolData.bin", mimeType="application/octet-stream")]
   public class StrPoolData extends ByteArray
   {
      
      public function StrPoolData()
      {
         /*
          * 反编译出错
          * 到达超时限制 (1 分) 
          * 指令数: 122
          */
         throw new flash.errors.IllegalOperationError("由于超时未反编译");
      }
   }
}

