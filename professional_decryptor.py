#!/usr/bin/env python3
"""
专业SWF解密工具
基于深度分析的高级解密算法
"""

import struct
import zlib
import io
import hashlib
import random
from pathlib import Path

class ProfessionalDecryptor:
    def __init__(self):
        self.binary_data_path = Path("extracted/binaryData")
        self.output_path = Path("professional_decrypt")
        self.output_path.mkdir(exist_ok=True)
        
        # 从ActionScript代码中提取的关键参数
        self.magic_numbers = [
            80729, 2500184, 5339833, 9342835, 15323847,
            15604630, 15227702, 13453492, 9384824
        ]
        
    def analyze_file_entropy(self, data):
        """分析文件熵值来判断加密强度"""
        if not data:
            return 0
            
        # 计算字节频率
        freq = [0] * 256
        for byte in data:
            freq[byte] += 1
        
        # 计算熵值
        import math
        entropy = 0
        length = len(data)
        for count in freq:
            if count > 0:
                p = count / length
                entropy -= p * math.log2(p)
        
        return entropy
    
    def detect_encryption_type(self, data):
        """检测加密类型"""
        if len(data) < 16:
            return "unknown"
            
        header = data[:16]
        entropy = self.analyze_file_entropy(data[:1024])
        
        # 检查常见的加密特征
        if data[:3] == b'CWS':
            return "swf_compressed"
        elif data[:3] == b'FWS':
            return "swf_uncompressed"
        elif entropy > 7.5:
            return "strong_encryption"
        elif entropy > 6.0:
            return "weak_encryption"
        elif b'\x78\x9c' in header or b'\x78\xda' in header:
            return "zlib_compressed"
        else:
            return "custom_encryption"
    
    def try_xor_decryption(self, data, key_candidates):
        """尝试XOR解密"""
        results = []
        
        for key in key_candidates:
            # 将数字转换为字节
            if isinstance(key, int):
                key_bytes = key.to_bytes(4, 'little')
            else:
                key_bytes = key
            
            decrypted = bytearray()
            for i, byte in enumerate(data):
                key_byte = key_bytes[i % len(key_bytes)]
                decrypted.append(byte ^ key_byte)
            
            # 检查解密结果的质量
            entropy = self.analyze_file_entropy(decrypted[:1024])
            printable_ratio = sum(1 for b in decrypted[:1000] if 32 <= b <= 126) / min(1000, len(decrypted))
            
            results.append({
                'key': key,
                'data': bytes(decrypted),
                'entropy': entropy,
                'printable_ratio': printable_ratio,
                'score': printable_ratio * (8 - entropy)  # 综合评分
            })
        
        # 按评分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results
    
    def try_substitution_decryption(self, data):
        """尝试替换解密"""
        results = []
        
        # 尝试简单的数学运算
        operations = [
            lambda x: (x - 1) % 256,
            lambda x: (x + 1) % 256,
            lambda x: x ^ 0xFF,
            lambda x: (x - 42) % 256,
            lambda x: (x + 42) % 256,
            lambda x: ((x << 1) | (x >> 7)) & 0xFF,
            lambda x: ((x >> 1) | (x << 7)) & 0xFF
        ]
        
        for i, op in enumerate(operations):
            decrypted = bytearray()
            for byte in data:
                decrypted.append(op(byte))
            
            entropy = self.analyze_file_entropy(decrypted[:1024])
            printable_ratio = sum(1 for b in decrypted[:1000] if 32 <= b <= 126) / min(1000, len(decrypted))
            
            results.append({
                'method': f'operation_{i}',
                'data': bytes(decrypted),
                'entropy': entropy,
                'printable_ratio': printable_ratio,
                'score': printable_ratio * (8 - entropy)
            })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        return results
    
    def try_advanced_decryption(self, data):
        """尝试高级解密算法"""
        results = []
        
        # 基于观察到的ActionScript代码的解密算法
        # 模拟 si8(§§pop() - 1,0) 和随机数操作
        
        for seed in [12345, 54321, 80729, 0]:
            random.seed(seed)
            decrypted = bytearray(data)
            
            # 应用类似的变换
            for i in range(min(len(decrypted), 1000)):
                if i < 8:  # 前8字节特殊处理
                    decrypted[i] = (decrypted[i] - 1) % 256
                    rand_val = int(random.random() * 80729) % 256
                    decrypted[i] = decrypted[i] ^ rand_val
                else:
                    # 后续字节使用不同的算法
                    decrypted[i] = (decrypted[i] ^ (i % 256)) % 256
            
            entropy = self.analyze_file_entropy(decrypted[:1024])
            printable_ratio = sum(1 for b in decrypted[:1000] if 32 <= b <= 126) / min(1000, len(decrypted))
            
            results.append({
                'method': f'advanced_seed_{seed}',
                'data': bytes(decrypted),
                'entropy': entropy,
                'printable_ratio': printable_ratio,
                'score': printable_ratio * (8 - entropy)
            })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        return results
    
    def extract_strings_from_data(self, data, min_length=4):
        """从数据中提取可能的字符串"""
        strings = []
        current_string = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII字符
                current_string += chr(byte)
            else:
                if len(current_string) >= min_length:
                    strings.append(current_string)
                current_string = ""
        
        # 添加最后一个字符串
        if len(current_string) >= min_length:
            strings.append(current_string)
        
        return strings
    
    def analyze_swf_structure(self, data):
        """分析可能的SWF结构"""
        if len(data) < 8:
            return None
            
        # 尝试作为SWF文件解析
        try:
            signature = data[:3]
            if signature in [b'FWS', b'CWS']:
                version = data[3]
                file_size = struct.unpack('<I', data[4:8])[0]
                
                info = {
                    'is_swf': True,
                    'signature': signature.decode(),
                    'version': version,
                    'declared_size': file_size,
                    'actual_size': len(data),
                    'size_match': abs(file_size - len(data)) < 1000
                }
                
                if signature == b'CWS':
                    try:
                        decompressed = zlib.decompress(data[8:])
                        info['decompressed_size'] = len(decompressed)
                        info['compression_ratio'] = len(data[8:]) / len(decompressed)
                    except:
                        info['decompression_failed'] = True
                
                return info
        except:
            pass
        
        return {'is_swf': False}
    
    def decrypt_file(self, filename):
        """解密单个文件"""
        print(f"\n分析文件: {filename}")
        print("=" * 50)
        
        file_path = self.binary_data_path / filename
        if not file_path.exists():
            print(f"文件不存在: {filename}")
            return None
            
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        
        # 检测加密类型
        encryption_type = self.detect_encryption_type(data)
        print(f"检测到的加密类型: {encryption_type}")
        
        # 分析原始数据
        original_entropy = self.analyze_file_entropy(data)
        print(f"原始数据熵值: {original_entropy:.2f}")
        
        results = []
        
        # 如果是压缩数据，先尝试解压
        if encryption_type in ['swf_compressed', 'zlib_compressed']:
            try:
                if data[:3] == b'CWS':
                    decompressed = zlib.decompress(data[8:])
                    header = data[:8]
                    full_decompressed = header[:3].replace(b'C', b'F') + header[3:] + decompressed
                else:
                    decompressed = zlib.decompress(data)
                    full_decompressed = decompressed
                
                print(f"解压成功，大小: {len(full_decompressed)} 字节")
                
                # 分析解压后的数据
                swf_info = self.analyze_swf_structure(full_decompressed)
                if swf_info['is_swf']:
                    print("解压后确认为有效SWF文件")
                    results.append({
                        'method': 'decompression',
                        'data': full_decompressed,
                        'entropy': self.analyze_file_entropy(full_decompressed),
                        'score': 10.0,  # 高分
                        'info': swf_info
                    })
                
            except Exception as e:
                print(f"解压失败: {e}")
        
        # 尝试XOR解密
        print("尝试XOR解密...")
        xor_results = self.try_xor_decryption(data, self.magic_numbers)
        results.extend(xor_results[:3])  # 取前3个最佳结果
        
        # 尝试替换解密
        print("尝试替换解密...")
        sub_results = self.try_substitution_decryption(data)
        results.extend(sub_results[:3])
        
        # 尝试高级解密
        print("尝试高级解密...")
        adv_results = self.try_advanced_decryption(data)
        results.extend(adv_results[:3])
        
        # 按评分排序所有结果
        results.sort(key=lambda x: x['score'], reverse=True)
        
        # 保存最佳结果
        best_results = results[:5]
        for i, result in enumerate(best_results):
            method = result.get('method', f'method_{i}')
            output_file = self.output_path / f"{filename}_{method}.bin"
            
            with open(output_file, 'wb') as f:
                f.write(result['data'])
            
            print(f"保存结果 {i+1}: {method} (评分: {result['score']:.2f})")
            
            # 提取字符串
            strings = self.extract_strings_from_data(result['data'])
            if strings:
                string_file = self.output_path / f"{filename}_{method}_strings.txt"
                with open(string_file, 'w', encoding='utf-8') as f:
                    for string in strings[:50]:  # 限制数量
                        f.write(f"{string}\n")
                print(f"  提取到 {len(strings)} 个字符串")
            
            # 检查是否为SWF
            swf_info = self.analyze_swf_structure(result['data'])
            if swf_info.get('is_swf'):
                print(f"  检测到SWF文件: {swf_info}")
        
        return best_results
    
    def run_professional_decryption(self):
        """运行专业解密"""
        print("开始专业SWF解密分析...")
        print("=" * 60)
        
        files_to_decrypt = [
            "2_zero.enc$$$$$#1315315a.GameCodesSWFData.bin",
            "3_zero.enc$$$$$#1315315a.GameArtworksSWFData.bin", 
            "4_zero.enc$$$$$#1315315a.StrPoolData.bin",
            "1_zero.enc$$$$$#1315315a.AS3LoopSWFData.bin"
        ]
        
        all_results = {}
        
        for filename in files_to_decrypt:
            results = self.decrypt_file(filename)
            if results:
                all_results[filename] = results
        
        # 生成总结报告
        self.generate_summary_report(all_results)
        
        print(f"\n专业解密完成！结果保存在: {self.output_path}")
    
    def generate_summary_report(self, all_results):
        """生成总结报告"""
        report_file = self.output_path / "professional_analysis_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 专业SWF解密分析报告\n\n")
            f.write(f"生成时间: {Path().cwd()}\n\n")
            
            for filename, results in all_results.items():
                f.write(f"## {filename}\n\n")
                
                for i, result in enumerate(results[:3]):
                    method = result.get('method', f'method_{i}')
                    f.write(f"### 方法 {i+1}: {method}\n")
                    f.write(f"- 评分: {result['score']:.2f}\n")
                    f.write(f"- 熵值: {result['entropy']:.2f}\n")
                    f.write(f"- 可打印字符比例: {result.get('printable_ratio', 0):.2f}\n")
                    
                    if 'info' in result:
                        f.write(f"- SWF信息: {result['info']}\n")
                    
                    f.write("\n")
                
                f.write("\n")

def main():
    decryptor = ProfessionalDecryptor()
    decryptor.run_professional_decryption()

if __name__ == "__main__":
    main()
