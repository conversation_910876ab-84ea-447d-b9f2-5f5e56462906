package zero.§enc$$$$$#1315315a§
{
   import flash.utils.ByteArray;
   
   [Embed(source="/_assets/2_zero.enc$$$$$#1315315a.GameCodesSWFData.bin", mimeType="application/octet-stream")]
   public class GameCodesSWFData extends ByteArray
   {
      
      public function GameCodesSWFData()
      {
         /*
          * 反编译出错
          * 到达超时限制 (1 分) 
          * 指令数: 101
          */
         throw new flash.errors.IllegalOperationError("由于超时未反编译");
      }
   }
}

