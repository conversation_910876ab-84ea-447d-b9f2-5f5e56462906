package zero.§enc$$$$$#1315315a§
{
   import flash.display.DisplayObject;
   import flash.display.Loader;
   
   public class Loader extends flash.display.Loader
   {
      
      public function Loader()
      {
         /*
          * 反编译出错
          * 到达超时限制 (1 分) 
          * 指令数: 144
          */
         throw new flash.errors.IllegalOperationError("由于超时未反编译");
      }
      
      override public function addChild(child:DisplayObject) : DisplayObject
      {
         if(Object)
         {
         }
         return siage.addChild(child);
      }
      
      override public function removeChild(child:DisplayObject) : DisplayObject
      {
         this.unload();
         return null;
      }
   }
}

