N9&v
?)S/:
>OA"
void
flash.events
Event
AS3Loop
flash.display
Sprite
String
enterFrame
ENTER_FRAME
addEventListener
flash.display.IBitmapDrawable,flash.display.IGraphicsData,flash.display.IGraphicsFill,flash.display.IGraphicsPath,flash.display.IGraphicsStroke,flash.display.ActionScriptVersion,flash.display.AVM1Movie,flash.display.Bitmap,flash.display.BitmapData,flash.display.BitmapDataChannel,flash.display.BlendMode,flash.display.CapsStyle,flash.display.ColorCorrection,flash.display.ColorCorrectionSupport,flash.display.DisplayObject,flash.display.DisplayObjectContainer,flash.display.FocusDirection,flash.display.FrameLabel,flash.display.GradientType,flash.display.Graphics,flash.display.GraphicsBitmapFill,flash.display.GraphicsEndFill,flash.display.GraphicsGradientFill,flash.display.GraphicsPath,flash.display.GraphicsPathCommand,flash.display.GraphicsPathWinding,flash.display.GraphicsShaderFill,flash.display.GraphicsSolidFill,flash.display.GraphicsStroke,flash.display.GraphicsTrianglePath,flash.display.InteractiveObject,flash.display.InterpolationMethod,flash.display.JointStyle,flash.display.LineScaleMode,flash.display.Loader,flash.display.LoaderInfo,flash.display.MorphShape,flash.display.MovieClip,flash.display.NativeMenu,flash.display.NativeMenuItem,flash.display.NativeWindow,flash.display.NativeWindowDisplayState,flash.display.NativeWindowInitOptions,flash.display.NativeWindowRenderMode,flash.display.NativeWindowResize,flash.display.NativeWindowSystemChrome,flash.display.NativeWindowType,flash.display.PixelSnapping,flash.display.Scene,flash.display.Screen,flash.display.Shader,flash.display.ShaderData,flash.display.ShaderInput,flash.display.ShaderJob,flash.display.ShaderParameter,flash.display.ShaderParameterType,flash.display.ShaderPrecision,flash.display.Shape,flash.display.SimpleButton,flash.display.SpreadMethod,flash.display.Sprite,flash.display.Stage,flash.display.Stage3D,flash.display.StageAlign,flash.display.StageAspectRatio,flash.display.StageDisplayState,flash.display.StageOrientation,flash.display.StageQuality,flash.display.StageScaleMode,flash.display.SWFVersion,flash.display.TriangleCulling
addChild
flash.net
navigateToURL
URLRequest
http://zero.flashwing.net
_blank
Object
EventDispatcher
DisplayObject
InteractiveObject
DisplayObjectContainer
AS3Loop
