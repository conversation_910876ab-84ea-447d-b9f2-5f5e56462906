#!/usr/bin/env python3
"""
SWF解密工具套件主控制脚本
运行所有解密工具并生成综合报告
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description):
    """运行命令并显示进度"""
    print(f"\n{'='*60}")
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        end_time = time.time()
        
        print(f"执行时间: {end_time - start_time:.2f} 秒")
        
        if result.returncode == 0:
            print("✅ 执行成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print("❌ 执行失败")
            if result.stderr:
                print("错误:")
                print(result.stderr)
        
        return result.returncode == 0, result.stdout, result.stderr
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False, "", str(e)

def check_prerequisites():
    """检查前置条件"""
    print("检查前置条件...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    # 检查必要的目录
    required_dirs = ["extracted", "extracted/binaryData", "extracted/scripts"]
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            print(f"❌ 缺少必要目录: {dir_path}")
            print("请先运行FFDec提取SWF文件内容")
            return False
    
    # 检查必要的文件
    required_files = [
        "extracted/binaryData/2_zero.enc$$$$$#1315315a.GameCodesSWFData.bin",
        "extracted/binaryData/3_zero.enc$$$$$#1315315a.GameArtworksSWFData.bin",
        "extracted/binaryData/4_zero.enc$$$$$#1315315a.StrPoolData.bin"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少必要文件: {file_path}")
            return False
    
    print("✅ 前置条件检查通过")
    return True

def create_output_directories():
    """创建输出目录"""
    print("创建输出目录...")
    
    directories = [
        "decrypted",
        "advanced_analysis", 
        "decrypted_strings",
        "final_report"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")

def run_basic_decryption():
    """运行基础解密"""
    return run_command(
        "python swf_decryptor.py launcher.swf",
        "基础SWF解密分析"
    )

def run_advanced_analysis():
    """运行高级分析"""
    return run_command(
        "python advanced_swf_analyzer.py",
        "高级SWF结构分析"
    )

def run_string_decryption():
    """运行字符串解密"""
    return run_command(
        "python string_decryptor.py",
        "字符串池解密分析"
    )

def generate_final_report():
    """生成最终报告"""
    print("\n生成最终综合报告...")
    
    report_path = Path("final_report/comprehensive_analysis.md")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# SWF文件解密分析综合报告\n\n")
        f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 1. 分析概述\n\n")
        f.write("本报告包含了对launcher.swf文件的全面解密分析结果，包括：\n")
        f.write("- 基础SWF结构分析\n")
        f.write("- 高级标签和字节码分析\n")
        f.write("- 字符串池解密\n")
        f.write("- 加密数据提取\n\n")
        
        # 读取各个分析结果
        f.write("## 2. 基础解密结果\n\n")
        basic_results = Path("decrypted")
        if basic_results.exists():
            f.write("### 解密文件列表\n")
            for file_path in basic_results.glob("*"):
                f.write(f"- {file_path.name} ({file_path.stat().st_size} 字节)\n")
            f.write("\n")
            
            # 读取字符串文件
            strings_file = basic_results / "strings.txt"
            if strings_file.exists():
                f.write("### 提取的字符串（前20个）\n")
                f.write("```\n")
                with open(strings_file, 'r', encoding='utf-8') as sf:
                    lines = sf.readlines()[:20]
                    f.writelines(lines)
                f.write("```\n\n")
        
        f.write("## 3. 高级分析结果\n\n")
        advanced_report = Path("advanced_analysis/analysis_report.txt")
        if advanced_report.exists():
            f.write("### SWF标签分析\n")
            with open(advanced_report, 'r', encoding='utf-8') as af:
                content = af.read()
                f.write("```\n")
                f.write(content[:2000])  # 限制长度
                f.write("\n...\n```\n\n")
        
        f.write("## 4. 字符串解密结果\n\n")
        string_results = Path("decrypted_strings")
        if string_results.exists():
            # 读取分类字符串
            categorized_file = string_results / "categorized_strings.txt"
            if categorized_file.exists():
                f.write("### 分类字符串结果\n")
                with open(categorized_file, 'r', encoding='utf-8') as cf:
                    content = cf.read()
                    f.write("```\n")
                    f.write(content[:1500])  # 限制长度
                    f.write("\n...\n```\n\n")
        
        f.write("## 5. 发现的关键信息\n\n")
        f.write("### 5.1 文件结构\n")
        f.write("- launcher.swf使用了多层加密和混淆\n")
        f.write("- 包含4个主要的加密数据块\n")
        f.write("- 使用了复杂的ActionScript字节码保护\n\n")
        
        f.write("### 5.2 保护机制\n")
        f.write("- 代码混淆：使用特殊字符和数字组合\n")
        f.write("- 反调试：生成大量虚假SWF文件\n")
        f.write("- 动态解密：运行时解密关键数据\n")
        f.write("- 内存操作：直接操作字节数据\n\n")
        
        f.write("### 5.3 建议的进一步分析\n")
        f.write("- 使用Flash调试器进行动态分析\n")
        f.write("- 在运行时转储解密后的内存数据\n")
        f.write("- 分析网络通信行为\n")
        f.write("- 研究反混淆技术\n\n")
        
        f.write("## 6. 工具使用说明\n\n")
        f.write("本分析使用了以下工具：\n")
        f.write("1. `swf_decryptor.py` - 基础SWF解密\n")
        f.write("2. `advanced_swf_analyzer.py` - 高级结构分析\n")
        f.write("3. `string_decryptor.py` - 字符串解密\n")
        f.write("4. `run_decryption.py` - 主控制脚本\n\n")
        
        f.write("所有工具的源代码和详细文档请参考项目目录。\n")
    
    print(f"✅ 综合报告已生成: {report_path}")

def main():
    """主函数"""
    print("SWF解密工具套件")
    print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先准备必要的文件")
        sys.exit(1)
    
    # 创建输出目录
    create_output_directories()
    
    # 运行各个解密工具
    success_count = 0
    total_count = 3
    
    print(f"\n开始运行 {total_count} 个解密工具...")
    
    # 1. 基础解密
    success, stdout, stderr = run_basic_decryption()
    if success:
        success_count += 1
    
    # 2. 高级分析
    success, stdout, stderr = run_advanced_analysis()
    if success:
        success_count += 1
    
    # 3. 字符串解密
    success, stdout, stderr = run_string_decryption()
    if success:
        success_count += 1
    
    # 生成最终报告
    generate_final_report()
    
    # 显示总结
    print(f"\n{'='*60}")
    print("解密分析完成!")
    print(f"成功执行: {success_count}/{total_count} 个工具")
    
    if success_count == total_count:
        print("✅ 所有工具执行成功")
    elif success_count > 0:
        print("⚠️ 部分工具执行成功")
    else:
        print("❌ 所有工具执行失败")
    
    print(f"\n结果文件位置:")
    print(f"- 基础解密结果: ./decrypted/")
    print(f"- 高级分析结果: ./advanced_analysis/")
    print(f"- 字符串解密结果: ./decrypted_strings/")
    print(f"- 综合报告: ./final_report/comprehensive_analysis.md")
    
    print(f"\n建议下一步:")
    print(f"1. 查看综合报告了解分析结果")
    print(f"2. 检查解密的SWF文件")
    print(f"3. 分析提取的字符串")
    print(f"4. 考虑使用动态分析工具")

if __name__ == "__main__":
    main()
