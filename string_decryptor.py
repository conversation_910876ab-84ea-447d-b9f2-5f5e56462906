#!/usr/bin/env python3
"""
字符串解密工具
基于从ActionScript代码中提取的解密逻辑来解密字符串池
"""

import struct
import zlib
import io
from pathlib import Path

class StringDecryptor:
    def __init__(self):
        self.binary_data_path = Path("extracted/binaryData")
        self.output_path = Path("decrypted_strings")
        self.output_path.mkdir(exist_ok=True)
        
        # 从script_0.as中提取的字符串池偏移量和长度
        self.str_pool_config = [
            (23, 2500184, 11), (11, 5339833, 5), (12, 9342835, 5), (22, 15323847, 11),
            (8, 15604630, 4), (9, 15227702, 5), (2, 13453492, 144), (0, 9384824, 2),
            (18, 4544501, 2), (6, 14208091, 84), (3, 11423165, 2), (10, 6400252, 2),
            (16, 15686868, 2), (5, 11275867, 2), (4, 10573929, 2), (13, 6935541, 2),
            (15, 16603423, 2), (7, 13661015, 2), (17, 1634773, 2), (20, 11728613, 2),
            (1, 4872666, 2), (19, 65212, 2), (14, 10448627, 2), (21, 1769844, 2)
        ]
    
    def read_str_pool_data(self):
        """读取字符串池数据"""
        file_path = self.binary_data_path / "4_zero.enc$$$$$#1315315a.StrPoolData.bin"
        if not file_path.exists():
            print(f"字符串池文件不存在: {file_path}")
            return None
            
        with open(file_path, 'rb') as f:
            return f.read()
    
    def try_decompress_data(self, data):
        """尝试多种方法解压数据"""
        print("尝试解压字符串池数据...")
        
        # 方法1: 直接zlib解压
        try:
            decompressed = zlib.decompress(data)
            print(f"直接zlib解压成功，大小: {len(decompressed)}")
            return decompressed
        except Exception as e:
            print(f"直接zlib解压失败: {e}")
        
        # 方法2: 检查是否是SWF格式
        if data[:3] == b'CWS':
            try:
                # SWF压缩格式
                header = data[:8]
                compressed_data = data[8:]
                decompressed = zlib.decompress(compressed_data)
                print(f"SWF格式解压成功，大小: {len(decompressed)}")
                return header + decompressed
            except Exception as e:
                print(f"SWF格式解压失败: {e}")
        
        # 方法3: 尝试不同的偏移量
        for offset in [4, 8, 12, 16, 32]:
            try:
                decompressed = zlib.decompress(data[offset:])
                print(f"偏移{offset}字节解压成功，大小: {len(decompressed)}")
                return decompressed
            except:
                continue
        
        # 方法4: 尝试原始数据
        print("无法解压，使用原始数据")
        return data
    
    def extract_strings_by_offsets(self, data):
        """根据偏移量提取字符串"""
        strings = {}
        
        print(f"数据总大小: {len(data)} 字节")
        
        for index, offset, length in self.str_pool_config:
            if offset >= len(data):
                print(f"偏移量 {offset} 超出数据范围 (索引 {index})")
                continue
            
            # 提取指定长度的数据
            end_pos = min(offset + length, len(data))
            string_data = data[offset:end_pos]
            
            # 尝试不同的解码方法
            decoded_strings = []
            
            # UTF-8解码
            try:
                # 查找null终止符
                null_pos = string_data.find(b'\x00')
                if null_pos != -1:
                    string_data = string_data[:null_pos]
                
                decoded = string_data.decode('utf-8', errors='ignore').strip()
                if decoded:
                    decoded_strings.append(('UTF-8', decoded))
            except:
                pass
            
            # ASCII解码
            try:
                decoded = string_data.decode('ascii', errors='ignore').strip()
                if decoded and decoded not in [s[1] for s in decoded_strings]:
                    decoded_strings.append(('ASCII', decoded))
            except:
                pass
            
            # Latin-1解码
            try:
                decoded = string_data.decode('latin-1', errors='ignore').strip()
                if decoded and decoded not in [s[1] for s in decoded_strings]:
                    decoded_strings.append(('Latin-1', decoded))
            except:
                pass
            
            # 十六进制表示
            hex_data = string_data.hex()
            
            strings[index] = {
                'offset': offset,
                'length': length,
                'raw_data': string_data,
                'hex_data': hex_data,
                'decoded_strings': decoded_strings
            }
        
        return strings
    
    def analyze_string_patterns(self, strings):
        """分析字符串模式"""
        patterns = {
            'urls': [],
            'file_paths': [],
            'function_names': [],
            'class_names': [],
            'encrypted_data': [],
            'readable_text': []
        }
        
        for index, string_info in strings.items():
            for encoding, text in string_info['decoded_strings']:
                # URL模式
                if any(protocol in text.lower() for protocol in ['http://', 'https://', 'ftp://']):
                    patterns['urls'].append((index, encoding, text))
                
                # 文件路径模式
                elif any(ext in text.lower() for ext in ['.swf', '.as', '.xml', '.txt', '.bin']):
                    patterns['file_paths'].append((index, encoding, text))
                
                # 函数名模式 (包含括号或以动词开头)
                elif '(' in text or any(text.lower().startswith(verb) for verb in ['get', 'set', 'load', 'init', 'start', 'stop']):
                    patterns['function_names'].append((index, encoding, text))
                
                # 类名模式 (首字母大写)
                elif text and text[0].isupper() and text.isalnum():
                    patterns['class_names'].append((index, encoding, text))
                
                # 可读文本
                elif len(text) > 3 and text.isprintable():
                    patterns['readable_text'].append((index, encoding, text))
                
                # 可能的加密数据 (包含特殊字符或看起来像base64)
                else:
                    patterns['encrypted_data'].append((index, encoding, text))
        
        return patterns
    
    def save_results(self, strings, patterns):
        """保存解密结果"""
        # 保存所有字符串
        with open(self.output_path / "all_strings.txt", 'w', encoding='utf-8') as f:
            f.write("所有提取的字符串\n")
            f.write("=" * 50 + "\n\n")
            
            for index in sorted(strings.keys()):
                string_info = strings[index]
                f.write(f"索引 {index}:\n")
                f.write(f"  偏移量: {string_info['offset']}\n")
                f.write(f"  长度: {string_info['length']}\n")
                f.write(f"  十六进制: {string_info['hex_data']}\n")
                f.write(f"  解码结果:\n")
                
                for encoding, text in string_info['decoded_strings']:
                    f.write(f"    {encoding}: {repr(text)}\n")
                
                f.write("\n")
        
        # 保存分类结果
        with open(self.output_path / "categorized_strings.txt", 'w', encoding='utf-8') as f:
            f.write("分类字符串结果\n")
            f.write("=" * 50 + "\n\n")
            
            for category, items in patterns.items():
                if items:
                    f.write(f"{category.upper()}:\n")
                    f.write("-" * 20 + "\n")
                    for index, encoding, text in items:
                        f.write(f"  [{index}] ({encoding}): {text}\n")
                    f.write("\n")
        
        # 保存原始数据
        for index, string_info in strings.items():
            if string_info['raw_data']:
                filename = f"string_{index:02d}_offset_{string_info['offset']}.bin"
                with open(self.output_path / filename, 'wb') as f:
                    f.write(string_info['raw_data'])
        
        print(f"结果已保存到: {self.output_path}")
    
    def decrypt_with_algorithm(self, data):
        """使用观察到的解密算法"""
        print("尝试使用解密算法...")
        
        # 模拟ActionScript中的解密过程
        decrypted = bytearray(data)
        
        # 应用类似于代码中的变换
        for i in range(min(len(decrypted), 1000)):  # 限制处理范围
            # 模拟 si8(§§pop() - 1,0) 操作
            if i < len(decrypted):
                original_value = decrypted[i]
                # 尝试不同的变换
                decrypted[i] = (original_value - 1) % 256
                
                # 如果结果不可打印，尝试其他变换
                if not (32 <= decrypted[i] <= 126):
                    decrypted[i] = (original_value + 1) % 256
                    
                if not (32 <= decrypted[i] <= 126):
                    decrypted[i] = original_value ^ 0xFF
                    
                if not (32 <= decrypted[i] <= 126):
                    decrypted[i] = original_value  # 保持原值
        
        return bytes(decrypted)
    
    def run_decryption(self):
        """运行字符串解密"""
        print("开始字符串解密...")
        
        # 读取数据
        raw_data = self.read_str_pool_data()
        if not raw_data:
            return
        
        print(f"原始数据大小: {len(raw_data)} 字节")
        
        # 尝试解压
        decompressed_data = self.try_decompress_data(raw_data)
        
        # 尝试解密算法
        decrypted_data = self.decrypt_with_algorithm(decompressed_data)
        
        # 保存处理后的数据
        with open(self.output_path / "decompressed_data.bin", 'wb') as f:
            f.write(decompressed_data)
        
        with open(self.output_path / "decrypted_data.bin", 'wb') as f:
            f.write(decrypted_data)
        
        # 从两种数据中提取字符串
        print("\n从解压数据中提取字符串...")
        strings_decompressed = self.extract_strings_by_offsets(decompressed_data)
        
        print("\n从解密数据中提取字符串...")
        strings_decrypted = self.extract_strings_by_offsets(decrypted_data)
        
        # 分析模式
        patterns_decompressed = self.analyze_string_patterns(strings_decompressed)
        patterns_decrypted = self.analyze_string_patterns(strings_decrypted)
        
        # 保存结果
        print("\n保存解压数据结果...")
        decompressed_output = self.output_path / "decompressed"
        decompressed_output.mkdir(exist_ok=True)
        
        with open(decompressed_output / "strings.txt", 'w', encoding='utf-8') as f:
            f.write("解压数据字符串\n")
            f.write("=" * 30 + "\n\n")
            for category, items in patterns_decompressed.items():
                if items:
                    f.write(f"{category}:\n")
                    for index, encoding, text in items:
                        f.write(f"  [{index}]: {text}\n")
                    f.write("\n")
        
        print("保存解密数据结果...")
        decrypted_output = self.output_path / "decrypted"
        decrypted_output.mkdir(exist_ok=True)
        
        with open(decrypted_output / "strings.txt", 'w', encoding='utf-8') as f:
            f.write("解密数据字符串\n")
            f.write("=" * 30 + "\n\n")
            for category, items in patterns_decrypted.items():
                if items:
                    f.write(f"{category}:\n")
                    for index, encoding, text in items:
                        f.write(f"  [{index}]: {text}\n")
                    f.write("\n")
        
        print("字符串解密完成!")
        
        # 显示一些结果
        print("\n发现的有趣字符串:")
        for category in ['urls', 'file_paths', 'function_names', 'readable_text']:
            items = patterns_decrypted.get(category, []) + patterns_decompressed.get(category, [])
            if items:
                print(f"\n{category}:")
                for index, encoding, text in items[:5]:  # 显示前5个
                    print(f"  [{index}]: {text}")

def main():
    decryptor = StringDecryptor()
    decryptor.run_decryption()

if __name__ == "__main__":
    main()
