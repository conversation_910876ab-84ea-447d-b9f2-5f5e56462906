#!/usr/bin/env python3
"""
SWF Decryptor Tool
用于解密和分析launcher.swf文件的工具
"""

import os
import sys
import struct
import zlib
import random
from pathlib import Path

class SWFDecryptor:
    def __init__(self, swf_path):
        self.swf_path = Path(swf_path)
        self.extracted_path = Path("extracted")
        self.binary_data_path = self.extracted_path / "binaryData"
        
        # 从分析中提取的字符串池偏移量
        self.str_pool_offsets = {
            23: 2500184, 11: 5339833, 12: 9342835, 22: 15323847,
            8: 15604630, 9: 15227702, 2: 13453492, 0: 9384824,
            18: 4544501, 6: 14208091, 3: 11423165, 10: 6400252,
            16: 15686868, 5: 11275867, 4: 10573929, 13: 6935541,
            15: 16603423, 7: 13661015, 17: 1634773, 20: 11728613,
            1: 4872666, 19: 65212, 14: 10448627, 21: 1769844
        }
        
    def read_binary_file(self, filename):
        """读取二进制文件"""
        try:
            file_path = self.binary_data_path / filename
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {filename}: {e}")
            return None
    
    def decompress_str_pool(self):
        """解压字符串池数据"""
        print("正在解压字符串池数据...")
        
        str_pool_data = self.read_binary_file("4_zero.enc$$$$$#1315315a.StrPoolData.bin")
        if not str_pool_data:
            return None
            
        try:
            # 尝试zlib解压
            decompressed = zlib.decompress(str_pool_data)
            print(f"字符串池解压成功，大小: {len(decompressed)} 字节")
            return decompressed
        except Exception as e:
            print(f"字符串池解压失败: {e}")
            # 尝试其他解压方法
            return self.try_alternative_decompression(str_pool_data)
    
    def try_alternative_decompression(self, data):
        """尝试其他解压方法"""
        print("尝试其他解压方法...")
        
        # 检查是否有SWF压缩头
        if data[:3] == b'CWS':
            print("检测到CWS格式，尝试SWF解压...")
            try:
                # SWF压缩格式：前8字节是头部，后面是压缩数据
                header = data[:8]
                compressed_data = data[8:]
                decompressed = zlib.decompress(compressed_data)
                return header + decompressed
            except Exception as e:
                print(f"SWF解压失败: {e}")
        
        # 尝试跳过可能的头部
        for skip in [0, 4, 8, 16, 32]:
            try:
                decompressed = zlib.decompress(data[skip:])
                print(f"跳过{skip}字节后解压成功")
                return decompressed
            except:
                continue
                
        return None
    
    def extract_strings_from_pool(self, pool_data):
        """从字符串池中提取字符串"""
        if not pool_data:
            return []
            
        strings = []
        pos = 0
        
        # 使用偏移量提取字符串
        sorted_offsets = sorted(self.str_pool_offsets.items(), key=lambda x: x[1])
        
        for i, (index, offset) in enumerate(sorted_offsets):
            if offset >= len(pool_data):
                continue
                
            # 计算字符串长度
            if i < len(sorted_offsets) - 1:
                next_offset = sorted_offsets[i + 1][1]
                length = min(next_offset - offset, len(pool_data) - offset)
            else:
                length = len(pool_data) - offset
                
            if length > 0:
                try:
                    # 尝试UTF-8解码
                    string_data = pool_data[offset:offset + length]
                    # 查找字符串结束符
                    null_pos = string_data.find(b'\x00')
                    if null_pos != -1:
                        string_data = string_data[:null_pos]
                    
                    decoded_string = string_data.decode('utf-8', errors='ignore')
                    if decoded_string.strip():
                        strings.append(f"[{index}] {decoded_string}")
                except Exception as e:
                    print(f"解码字符串失败 (offset {offset}): {e}")
        
        return strings
    
    def decrypt_game_codes(self):
        """解密游戏代码数据"""
        print("正在解密游戏代码数据...")
        
        game_codes_data = self.read_binary_file("2_zero.enc$$$$$#1315315a.GameCodesSWFData.bin")
        if not game_codes_data:
            return None
            
        # 模拟解密过程
        decrypted_data = bytearray(game_codes_data)
        
        # 应用从代码中观察到的解密算法
        # 这是一个简化版本，实际算法可能更复杂
        for i in range(min(8, len(decrypted_data))):
            if i < len(decrypted_data):
                # 模拟 si8(§§pop() - 1,0) 操作
                decrypted_data[i] = (decrypted_data[i] - 1) % 256
                # 模拟随机数混合
                decrypted_data[i] = decrypted_data[i] ^ (int(random.random() * 80729) % 256)
        
        return bytes(decrypted_data)
    
    def analyze_swf_structure(self, data):
        """分析SWF文件结构"""
        if len(data) < 8:
            return None
            
        signature = data[:3]
        version = data[3]
        file_size = struct.unpack('<I', data[4:8])[0]
        
        info = {
            'signature': signature.decode('ascii', errors='ignore'),
            'version': version,
            'file_size': file_size,
            'is_compressed': signature == b'CWS'
        }
        
        if info['is_compressed']:
            try:
                # 解压SWF内容
                header = data[:8]
                compressed_content = data[8:]
                decompressed_content = zlib.decompress(compressed_content)
                info['decompressed_size'] = len(decompressed_content)
                info['compression_ratio'] = len(compressed_content) / len(decompressed_content)
            except Exception as e:
                info['decompression_error'] = str(e)
        
        return info
    
    def save_decrypted_data(self, data, filename):
        """保存解密后的数据"""
        output_path = Path("decrypted") / filename
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'wb') as f:
            f.write(data)
        print(f"解密数据已保存到: {output_path}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始SWF解密分析...")
        print("=" * 50)
        
        # 1. 解压字符串池
        str_pool_data = self.decompress_str_pool()
        if str_pool_data:
            strings = self.extract_strings_from_pool(str_pool_data)
            print(f"\n提取到 {len(strings)} 个字符串:")
            for string in strings[:20]:  # 显示前20个
                print(f"  {string}")
            if len(strings) > 20:
                print(f"  ... 还有 {len(strings) - 20} 个字符串")
            
            # 保存字符串到文件
            with open("decrypted/strings.txt", 'w', encoding='utf-8') as f:
                for string in strings:
                    f.write(string + '\n')
        
        # 2. 解密游戏代码
        game_codes = self.decrypt_game_codes()
        if game_codes:
            swf_info = self.analyze_swf_structure(game_codes)
            if swf_info:
                print(f"\n游戏代码SWF信息:")
                for key, value in swf_info.items():
                    print(f"  {key}: {value}")
            
            self.save_decrypted_data(game_codes, "game_codes.swf")
        
        # 3. 分析美术资源
        artwork_data = self.read_binary_file("3_zero.enc$$$$$#1315315a.GameArtworksSWFData.bin")
        if artwork_data:
            artwork_info = self.analyze_swf_structure(artwork_data)
            if artwork_info:
                print(f"\n美术资源SWF信息:")
                for key, value in artwork_info.items():
                    print(f"  {key}: {value}")
            
            self.save_decrypted_data(artwork_data, "game_artworks.swf")
        
        print("\n分析完成!")

def main():
    if len(sys.argv) != 2:
        print("用法: python swf_decryptor.py <launcher.swf路径>")
        sys.exit(1)
    
    swf_path = sys.argv[1]
    if not os.path.exists(swf_path):
        print(f"文件不存在: {swf_path}")
        sys.exit(1)
    
    decryptor = SWFDecryptor(swf_path)
    decryptor.run_analysis()

if __name__ == "__main__":
    main()
