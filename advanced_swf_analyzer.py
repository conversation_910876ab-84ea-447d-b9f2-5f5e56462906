#!/usr/bin/env python3
"""
高级SWF分析器
专门用于分析复杂的SWF文件结构和ActionScript字节码
"""

import struct
import zlib
import io
from pathlib import Path

class SWFTag:
    """SWF标签解析器"""
    
    TAG_NAMES = {
        0: "End",
        1: "ShowFrame",
        2: "DefineShape",
        4: "PlaceObject",
        5: "RemoveObject",
        6: "DefineBits",
        7: "DefineButton",
        8: "JPEGTables",
        9: "SetBackgroundColor",
        10: "DefineFont",
        11: "DefineText",
        12: "DoAction",
        13: "DefineFontInfo",
        14: "DefineSound",
        15: "StartSound",
        17: "DefineButtonSound",
        18: "SoundStreamHead",
        19: "SoundStreamBlock",
        20: "DefineBitsLossless",
        21: "DefineBitsJPEG2",
        22: "DefineShape2",
        23: "DefineButtonCxform",
        24: "Protect",
        26: "PlaceObject2",
        28: "RemoveObject2",
        32: "DefineShape3",
        33: "DefineText2",
        34: "DefineButton2",
        35: "DefineBitsJPEG3",
        36: "DefineBitsLossless2",
        37: "DefineEditText",
        39: "DefineSprite",
        43: "FrameLabel",
        46: "DefineMorphShape",
        48: "DefineFont2",
        56: "ExportAssets",
        57: "ImportAssets",
        58: "EnableDebugger",
        59: "DoInitAction",
        60: "DefineVideoStream",
        61: "VideoFrame",
        62: "DefineFontInfo2",
        64: "EnableDebugger2",
        65: "ScriptLimits",
        69: "FileAttributes",
        71: "ImportAssets2",
        73: "DefineFontAlignZones",
        74: "CSMTextSettings",
        75: "DefineFont3",
        76: "SymbolClass",
        77: "Metadata",
        78: "DefineScalingGrid",
        82: "DoABC",
        83: "DefineShape4",
        84: "DefineMorphShape2",
        86: "DefineSceneAndFrameLabelData",
        87: "DefineBinaryData",
        88: "DefineFontName"
    }
    
    def __init__(self, tag_type, length, data):
        self.tag_type = tag_type
        self.length = length
        self.data = data
        self.name = self.TAG_NAMES.get(tag_type, f"Unknown_{tag_type}")

class AdvancedSWFAnalyzer:
    def __init__(self):
        self.binary_data_path = Path("extracted/binaryData")
        self.output_path = Path("advanced_analysis")
        self.output_path.mkdir(exist_ok=True)
    
    def read_swf_header(self, data):
        """读取SWF文件头"""
        if len(data) < 8:
            return None
            
        signature = data[:3]
        version = data[3]
        file_size = struct.unpack('<I', data[4:8])[0]
        
        # 如果是压缩的SWF，解压内容
        if signature == b'CWS':
            try:
                header = data[:8]
                compressed_data = data[8:]
                decompressed_data = zlib.decompress(compressed_data)
                # 重建完整的未压缩SWF
                full_data = b'FWS' + data[3:8] + decompressed_data
                return {
                    'signature': signature.decode(),
                    'version': version,
                    'file_size': file_size,
                    'compressed': True,
                    'data': full_data
                }
            except Exception as e:
                print(f"解压失败: {e}")
                return None
        else:
            return {
                'signature': signature.decode(),
                'version': version,
                'file_size': file_size,
                'compressed': False,
                'data': data
            }
    
    def read_rect(self, stream):
        """读取SWF矩形结构"""
        # 读取第一个字节来确定位数
        first_byte = stream.read(1)[0]
        nbits = first_byte >> 3
        
        # 计算需要读取的总字节数
        total_bits = 5 + nbits * 4
        total_bytes = (total_bits + 7) // 8
        
        # 读取剩余字节
        remaining_bytes = stream.read(total_bytes - 1)
        all_bytes = bytes([first_byte]) + remaining_bytes
        
        # 这里简化处理，返回字节数
        return total_bytes
    
    def parse_swf_tags(self, data):
        """解析SWF标签"""
        header_info = self.read_swf_header(data)
        if not header_info:
            return []
            
        swf_data = header_info['data']
        stream = io.BytesIO(swf_data)
        
        # 跳过文件头 (8字节)
        stream.seek(8)
        
        # 跳过矩形 (可变长度)
        rect_size = self.read_rect(stream)
        
        # 跳过帧率和帧数 (4字节)
        stream.read(4)
        
        tags = []
        while True:
            # 读取标签头
            tag_header = stream.read(2)
            if len(tag_header) < 2:
                break
                
            tag_code_and_length = struct.unpack('<H', tag_header)[0]
            tag_type = tag_code_and_length >> 6
            length = tag_code_and_length & 0x3F
            
            # 如果长度是63，读取扩展长度
            if length == 0x3F:
                ext_length = stream.read(4)
                if len(ext_length) < 4:
                    break
                length = struct.unpack('<I', ext_length)[0]
            
            # 读取标签数据
            tag_data = stream.read(length)
            if len(tag_data) < length:
                break
                
            tag = SWFTag(tag_type, length, tag_data)
            tags.append(tag)
            
            # 如果是End标签，停止解析
            if tag_type == 0:
                break
        
        return tags
    
    def analyze_binary_data_tag(self, tag):
        """分析DefineBinaryData标签"""
        if tag.tag_type != 87:  # DefineBinaryData
            return None
            
        if len(tag.data) < 6:
            return None
            
        # 读取字符ID和保留字段
        char_id = struct.unpack('<H', tag.data[:2])[0]
        reserved = struct.unpack('<I', tag.data[2:6])[0]
        binary_data = tag.data[6:]
        
        return {
            'char_id': char_id,
            'reserved': reserved,
            'data_size': len(binary_data),
            'data': binary_data
        }
    
    def analyze_do_abc_tag(self, tag):
        """分析DoABC标签 (ActionScript字节码)"""
        if tag.tag_type != 82:  # DoABC
            return None
            
        if len(tag.data) < 4:
            return None
            
        flags = struct.unpack('<I', tag.data[:4])[0]
        
        # 查找字符串结束符
        name_end = tag.data.find(b'\x00', 4)
        if name_end == -1:
            return None
            
        name = tag.data[4:name_end].decode('utf-8', errors='ignore')
        abc_data = tag.data[name_end + 1:]
        
        return {
            'flags': flags,
            'name': name,
            'abc_size': len(abc_data),
            'abc_data': abc_data
        }
    
    def extract_encrypted_data(self):
        """提取所有加密数据"""
        print("提取加密数据...")
        
        files_to_analyze = [
            "2_zero.enc$$$$$#1315315a.GameCodesSWFData.bin",
            "3_zero.enc$$$$$#1315315a.GameArtworksSWFData.bin",
            "4_zero.enc$$$$$#1315315a.StrPoolData.bin",
            "1_zero.enc$$$$$#1315315a.AS3LoopSWFData.bin"
        ]
        
        results = {}
        
        for filename in files_to_analyze:
            file_path = self.binary_data_path / filename
            if not file_path.exists():
                print(f"文件不存在: {filename}")
                continue
                
            print(f"分析文件: {filename}")
            
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 分析文件头
            analysis = {
                'filename': filename,
                'size': len(data),
                'header_hex': data[:16].hex(),
                'is_swf': data[:3] in [b'FWS', b'CWS'],
                'is_compressed': data[:3] == b'CWS'
            }
            
            # 如果是SWF文件，解析标签
            if analysis['is_swf']:
                try:
                    tags = self.parse_swf_tags(data)
                    analysis['tag_count'] = len(tags)
                    analysis['tags'] = []
                    
                    for tag in tags:
                        tag_info = {
                            'type': tag.tag_type,
                            'name': tag.name,
                            'length': tag.length
                        }
                        
                        # 特殊处理某些标签
                        if tag.tag_type == 87:  # DefineBinaryData
                            binary_info = self.analyze_binary_data_tag(tag)
                            if binary_info:
                                tag_info['binary_data'] = binary_info
                        elif tag.tag_type == 82:  # DoABC
                            abc_info = self.analyze_do_abc_tag(tag)
                            if abc_info:
                                tag_info['abc_data'] = abc_info
                        
                        analysis['tags'].append(tag_info)
                        
                except Exception as e:
                    analysis['parse_error'] = str(e)
            
            # 尝试解压
            if analysis['is_compressed'] or not analysis['is_swf']:
                try:
                    # 尝试不同的解压方法
                    decompressed = None
                    
                    # 方法1: 直接zlib解压
                    try:
                        decompressed = zlib.decompress(data)
                        analysis['decompression_method'] = 'direct_zlib'
                    except:
                        pass
                    
                    # 方法2: 跳过SWF头部
                    if not decompressed and len(data) > 8:
                        try:
                            decompressed = zlib.decompress(data[8:])
                            analysis['decompression_method'] = 'skip_header'
                        except:
                            pass
                    
                    # 方法3: 尝试不同的偏移量
                    if not decompressed:
                        for offset in [4, 12, 16, 32]:
                            if len(data) > offset:
                                try:
                                    decompressed = zlib.decompress(data[offset:])
                                    analysis['decompression_method'] = f'offset_{offset}'
                                    break
                                except:
                                    continue
                    
                    if decompressed:
                        analysis['decompressed_size'] = len(decompressed)
                        analysis['compression_ratio'] = len(data) / len(decompressed)
                        
                        # 保存解压后的数据
                        output_file = self.output_path / f"decompressed_{filename}"
                        with open(output_file, 'wb') as f:
                            f.write(decompressed)
                        analysis['decompressed_file'] = str(output_file)
                        
                except Exception as e:
                    analysis['decompression_error'] = str(e)
            
            results[filename] = analysis
        
        return results
    
    def generate_report(self, results):
        """生成分析报告"""
        report_file = self.output_path / "analysis_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("SWF高级分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            for filename, analysis in results.items():
                f.write(f"文件: {filename}\n")
                f.write("-" * 30 + "\n")
                f.write(f"大小: {analysis['size']} 字节\n")
                f.write(f"文件头: {analysis['header_hex']}\n")
                f.write(f"是否为SWF: {analysis['is_swf']}\n")
                f.write(f"是否压缩: {analysis['is_compressed']}\n")
                
                if 'decompressed_size' in analysis:
                    f.write(f"解压后大小: {analysis['decompressed_size']} 字节\n")
                    f.write(f"压缩比: {analysis['compression_ratio']:.2f}\n")
                    f.write(f"解压方法: {analysis['decompression_method']}\n")
                
                if 'tag_count' in analysis:
                    f.write(f"SWF标签数量: {analysis['tag_count']}\n")
                    f.write("标签列表:\n")
                    for tag in analysis['tags']:
                        f.write(f"  {tag['name']} (类型: {tag['type']}, 长度: {tag['length']})\n")
                        if 'binary_data' in tag:
                            bd = tag['binary_data']
                            f.write(f"    二进制数据: ID={bd['char_id']}, 大小={bd['data_size']}\n")
                        if 'abc_data' in tag:
                            abc = tag['abc_data']
                            f.write(f"    ABC数据: 名称={abc['name']}, 大小={abc['abc_size']}\n")
                
                if 'parse_error' in analysis:
                    f.write(f"解析错误: {analysis['parse_error']}\n")
                
                if 'decompression_error' in analysis:
                    f.write(f"解压错误: {analysis['decompression_error']}\n")
                
                f.write("\n")
        
        print(f"分析报告已保存到: {report_file}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始高级SWF分析...")
        
        results = self.extract_encrypted_data()
        self.generate_report(results)
        
        print("高级分析完成!")
        return results

def main():
    analyzer = AdvancedSWFAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
